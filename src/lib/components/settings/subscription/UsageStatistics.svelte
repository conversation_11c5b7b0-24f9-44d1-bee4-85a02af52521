<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import type { UsageStats, SubscriptionTier } from '$lib/types/subscription';
	import {
		UsersSolid,
		ServerSolid,
		CogSolid,
		ExclamationCircleSolid,
		CheckCircleSolid
	} from 'flowbite-svelte-icons';

	export let usageStats: UsageStats;
	export let currentTier: SubscriptionTier | undefined = undefined;
	export let detailed: boolean = false;

	// Calculate usage percentages and status
	function calculateUsage(current: number | string, limit: string | number | boolean): {
		percentage: number;
		status: 'good' | 'warning' | 'critical';
		display: string;
	} {
		if (typeof limit === 'boolean' || limit === 'Unlimited') {
			return {
				percentage: 0,
				status: 'good',
				display: limit === 'Unlimited' ? 'Unlimited' : limit ? 'Available' : 'Not Available'
			};
		}

		const currentNum = typeof current === 'string' ? parseFloat(current) : current;
		const limitNum = typeof limit === 'string' ? parseFloat(limit) : limit;

		if (isNaN(currentNum) || isNaN(limitNum) || limitNum === 0) {
			return { percentage: 0, status: 'good', display: 'N/A' };
		}

		const percentage = (currentNum / limitNum) * 100;
		let status: 'good' | 'warning' | 'critical' = 'good';

		if (percentage >= 90) status = 'critical';
		else if (percentage >= 75) status = 'warning';

		return {
			percentage: Math.min(percentage, 100),
			status,
			display: `${currentNum} / ${limitNum}`
		};
	}

	// Usage items configuration
	$: usageItems = [
		{
			key: 'users',
			label: t('active_users') || 'Active Users',
			icon: UsersSolid,
			current: usageStats.currentUsers,
			limit: currentTier?.features['Active User Accounts'] || 'N/A',
			color: 'blue'
		},
		{
			key: 'storage',
			label: t('storage_used') || 'Storage Used',
			icon: ServerSolid,
			current: usageStats.storageUsed,
			limit: currentTier?.features['Storage'] || 'N/A',
			color: 'green',
			isStorage: true
		},
		{
			key: 'messages',
			label: t('messages_this_month') || 'Messages This Month',
			icon: CogSolid, // Using CogSolid as fallback for messages
			current: usageStats.messagesThisMonth,
			limit: currentTier?.features['Message/Minutes'] ?
				parseInt(currentTier.features['Message/Minutes'].toString()) * 60 : 'N/A',
			color: 'purple'
		},
		{
			key: 'workflows',
			label: t('active_workflows') || 'Active Workflows',
			icon: CogSolid,
			current: usageStats.workflowsActive,
			limit: currentTier?.features['AI Workflow (unit)'] || 'N/A',
			color: 'orange'
		}
	];

	function getStatusColor(status: string): string {
		switch (status) {
			case 'critical': return 'text-red-600 bg-red-100';
			case 'warning': return 'text-yellow-600 bg-yellow-100';
			default: return 'text-green-600 bg-green-100';
		}
	}

	function getProgressBarColor(status: string): string {
		switch (status) {
			case 'critical': return 'bg-red-500';
			case 'warning': return 'bg-yellow-500';
			default: return 'bg-green-500';
		}
	}

	function parseStorageValue(value: string): number {
		const match = value.match(/(\d+(?:\.\d+)?)\s*(GB|TB|MB)/i);
		if (!match) return 0;
		
		const num = parseFloat(match[1]);
		const unit = match[2].toUpperCase();
		
		switch (unit) {
			case 'TB': return num * 1024;
			case 'GB': return num;
			case 'MB': return num / 1024;
			default: return num;
		}
	}

	function calculateStorageUsage(current: string, limit: string | number | boolean) {
		if (typeof limit === 'boolean' || limit === 'Unlimited') {
			return {
				percentage: 0,
				status: 'good' as const,
				display: limit === 'Unlimited' ? 'Unlimited' : 'N/A'
			};
		}

		const currentGB = parseStorageValue(current);
		const limitGB = typeof limit === 'string' ? parseStorageValue(limit) : limit;

		if (limitGB === 0) {
			return { percentage: 0, status: 'good' as const, display: 'N/A' };
		}

		const percentage = (currentGB / limitGB) * 100;
		let status: 'good' | 'warning' | 'critical' = 'good';

		if (percentage >= 90) status = 'critical';
		else if (percentage >= 75) status = 'warning';

		return {
			percentage: Math.min(percentage, 100),
			status,
			display: `${current} / ${limit}`
		};
	}
</script>

<div class="space-y-6">
	<div class="bg-white rounded-lg shadow-md border p-6">
		<div class="flex items-center justify-between mb-6">
			<div>
				<h2 class="text-xl font-semibold text-gray-900">
					{t('usage_statistics') || 'Usage Statistics'}
				</h2>
				<p class="text-sm text-gray-600 mt-1">
					{t('current_usage_vs_limits') || 'Current usage compared to your plan limits'}
				</p>
			</div>
			{#if currentTier}
				<div class="text-right">
					<p class="text-sm text-gray-500">{t('current_plan') || 'Current Plan'}</p>
					<p class="font-semibold text-gray-900">{currentTier.name}</p>
				</div>
			{/if}
		</div>

		<!-- Usage Cards Grid -->
		<div class="grid grid-cols-1 md:grid-cols-2 {detailed ? 'lg:grid-cols-2' : 'lg:grid-cols-4'} gap-6">
			{#each usageItems as item}
				{@const usage = item.isStorage ? 
					calculateStorageUsage(item.current.toString(), item.limit) : 
					calculateUsage(item.current, item.limit)}
				
				<div class="bg-gray-50 rounded-lg p-4 border">
					<div class="flex items-center justify-between mb-3">
						<div class="flex items-center space-x-2">
							<div class="p-2 bg-{item.color}-100 rounded-lg">
								<svelte:component this={item.icon} class="h-5 w-5 text-{item.color}-600" />
							</div>
							<h3 class="font-medium text-gray-900 text-sm">{item.label}</h3>
						</div>
						
						{#if usage.status !== 'good'}
							<div class="flex items-center space-x-1">
								{#if usage.status === 'critical'}
									<ExclamationCircleSolid class="h-4 w-4 text-red-500" />
								{:else}
									<ExclamationCircleSolid class="h-4 w-4 text-yellow-500" />
								{/if}
							</div>
						{:else}
							<CheckCircleSolid class="h-4 w-4 text-green-500" />
						{/if}
					</div>

					<!-- Usage Display -->
					<div class="space-y-2">
						<div class="flex items-center justify-between">
							<span class="text-lg font-semibold text-gray-900">
								{typeof item.current === 'string' ? item.current : item.current.toLocaleString()}
							</span>
							<span class="text-xs px-2 py-1 rounded-full {getStatusColor(usage.status)}">
								{usage.percentage > 0 ? `${usage.percentage.toFixed(0)}%` : usage.display.includes('Unlimited') ? 'Unlimited' : 'Available'}
							</span>
						</div>

						{#if usage.percentage > 0}
							<!-- Progress Bar -->
							<div class="w-full bg-gray-200 rounded-full h-2">
								<div 
									class="h-2 rounded-full transition-all duration-300 {getProgressBarColor(usage.status)}"
									style="width: {usage.percentage}%"
								></div>
							</div>
							
							<p class="text-xs text-gray-600">
								{usage.display}
								{#if usage.status === 'critical'}
									- {t('limit_exceeded') || 'Limit nearly reached'}
								{:else if usage.status === 'warning'}
									- {t('approaching_limit') || 'Approaching limit'}
								{/if}
							</p>
						{:else}
							<p class="text-xs text-gray-600">{usage.display}</p>
						{/if}
					</div>
				</div>
			{/each}
		</div>

		{#if detailed}
			<!-- Detailed Usage Breakdown -->
			<div class="mt-8 pt-6 border-t border-gray-200">
				<h3 class="text-lg font-semibold text-gray-900 mb-4">
					{t('detailed_breakdown') || 'Detailed Usage Breakdown'}
				</h3>
				
				<div class="space-y-4">
					{#each usageItems as item}
						{@const usage = item.isStorage ? 
							calculateStorageUsage(item.current.toString(), item.limit) : 
							calculateUsage(item.current, item.limit)}
						
						<div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
							<div class="flex items-center space-x-3">
								<svelte:component this={item.icon} class="h-5 w-5 text-{item.color}-600" />
								<div>
									<h4 class="font-medium text-gray-900">{item.label}</h4>
									<p class="text-sm text-gray-600">
										{usage.display}
										{#if currentTier && item.key === 'messages'}
											({currentTier.features['Message/Minutes']} per minute limit)
										{/if}
									</p>
								</div>
							</div>
							
							<div class="text-right">
								{#if usage.percentage > 0}
									<span class="text-lg font-semibold text-gray-900">
										{usage.percentage.toFixed(1)}%
									</span>
									<p class="text-xs {getStatusColor(usage.status).split(' ')[0]}">
										{usage.status === 'critical' ? 'Critical' : 
										 usage.status === 'warning' ? 'Warning' : 'Good'}
									</p>
								{:else}
									<span class="text-sm text-gray-600">{usage.display}</span>
								{/if}
							</div>
						</div>
					{/each}
				</div>
			</div>

			<!-- Usage Recommendations -->
			<div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
				<h4 class="font-medium text-blue-900 mb-2">
					{t('usage_recommendations') || 'Usage Recommendations'}
				</h4>
				<ul class="text-sm text-blue-800 space-y-1">
					{#each usageItems as item}
						{@const usage = item.isStorage ? 
							calculateStorageUsage(item.current.toString(), item.limit) : 
							calculateUsage(item.current, item.limit)}
						
						{#if usage.status === 'critical'}
							<li>• Consider upgrading your plan to increase {item.label.toLowerCase()} limit</li>
						{:else if usage.status === 'warning'}
							<li>• Monitor {item.label.toLowerCase()} usage closely</li>
						{/if}
					{/each}
					<li>• Regularly review usage patterns to optimize your subscription</li>
				</ul>
			</div>
		{/if}
	</div>
</div>
