import { error, redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import { services } from '$lib/api/features';
import { SubscriptionTransformer } from '$lib/utils/subscriptionTransformer';

export const load: PageServerLoad = async ({ cookies }) => {
	let access_token = cookies.get('access_token');

	if (!access_token) {
		return {
			subscriptionData: null,
			systemSettings: {},
			userRole: null,
			error: 'No access token available'
		};
	}

	// Verify user authentication and role
	const response_userInfo = await services.users.getUserInfo(access_token);
	if (response_userInfo.res_status === 401) {
		throw error(401, 'Invalid access token!!!');
	}

	// Check if user has permission to view subscription settings
	// Note: getUserInfo returns a single user object, not an array despite the interface
	const user = response_userInfo.users as any;
	const role = user.roles?.[0]?.name;
	if (role !== 'Admin' && role !== 'Supervisor') {
		throw redirect(302, '/');
	}

	try {
		// Fetch subscription data from backend API
		const [quotaStatusResponse, subscriptionInfoResponse, tiersResponse] = await Promise.all([
			services.subscription.getQuotaStatus(access_token),
			services.subscription.getSubscriptionInfo(access_token),
			services.subscription.getAvailableTiers(access_token)
		]);

		// Check if quota status request was successful
		if (quotaStatusResponse.res_status !== 200 || !quotaStatusResponse.data) {
			console.error('Failed to fetch quota status:', quotaStatusResponse.error_msg);
			return {
				subscriptionData: null,
				systemSettings: {},
				userRole: role,
				error: quotaStatusResponse.error_msg || 'Failed to fetch subscription quota status'
			};
		}

		// Transform backend data to frontend format
		const subscriptionData = SubscriptionTransformer.transformToSubscriptionData(
			quotaStatusResponse.data,
			subscriptionInfoResponse.data,
			tiersResponse.tiers || []
		);

		// Get system settings for theming (if available)
		let systemSettings = {};
		try {
			const systemSettingsResponse = await services.system_setting.getAll(access_token);
			if (systemSettingsResponse.res_status === 200) {
				systemSettings = systemSettingsResponse.system_setting || {};
			}
		} catch (settingsError) {
			console.warn('Could not fetch system settings:', settingsError);
			// Continue without system settings
		}

		return {
			subscriptionData,
			systemSettings,
			userRole: role,
			error: null
		};
	} catch (err) {
		console.error('Error loading subscription data:', err);
		return {
			subscriptionData: null,
			systemSettings: {},
			userRole: role,
			error: 'Failed to load subscription data'
		};
	}
};
