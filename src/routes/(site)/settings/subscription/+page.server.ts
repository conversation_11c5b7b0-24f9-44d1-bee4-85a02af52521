import { error, redirect } from '@sveltejs/kit';
import type { PageServerLoad } from './$types';
import type { SubscriptionData } from '$lib/types/subscription';
import { services } from '$lib/api/features';
import { readFileSync } from 'fs';
import { join } from 'path';

export const load: PageServerLoad = async ({ cookies }) => {
	let access_token = cookies.get('access_token');
	let refresh_token = cookies.get('refresh_token');

	if (!access_token) {
		return {
			subscriptionData: null,
			error: 'No access token available'
		};
	}

	// Verify user authentication and role
	const response_userInfo = await services.users.getUserInfo(access_token);
	if (response_userInfo.res_status === 401) {
		throw error(401, 'Invalid access token!!!');
	}

	// Check if user has permission to view subscription settings
	const role = response_userInfo.users.roles[0].name;
	if (role !== 'Admin' && role !== 'Supervisor') {
		throw redirect(302, '/');
	}

	try {
		// Load subscription data from JSON file
		const subscriptionDataPath = join(process.cwd(), 'subscription-data.json');
		const subscriptionDataRaw = readFileSync(subscriptionDataPath, 'utf-8');
		const subscriptionData: SubscriptionData = JSON.parse(subscriptionDataRaw);

		// Get system settings for theming
		const systemSettingsResponse = await services.settings.getSystemSettings(access_token);
		let systemSettings = {};
		
		if (systemSettingsResponse.res_status === 200) {
			systemSettings = systemSettingsResponse.system_settings;
		}

		return {
			subscriptionData,
			systemSettings,
			userRole: role,
			error: null
		};
	} catch (err) {
		console.error('Error loading subscription data:', err);
		return {
			subscriptionData: null,
			systemSettings: {},
			userRole: role,
			error: 'Failed to load subscription data'
		};
	}
};
