<script lang="ts">
	import { t } from '$lib/stores/i18n';
	import type { PageData } from './$types';
	import type { SubscriptionTab } from '$lib/types/subscription';
	import { onMount } from 'svelte';
	import { Toast } from 'flowbite-svelte';
	import { CheckCircleSolid } from 'flowbite-svelte-icons';
	import { fly } from 'svelte/transition';

	// Import subscription components
	import CurrentSubscriptionCard from '$lib/components/settings/subscription/CurrentSubscriptionCard.svelte';
	import SubscriptionTiersComparison from '$lib/components/settings/subscription/SubscriptionTiersComparison.svelte';
	import UsageStatistics from '$lib/components/settings/subscription/UsageStatistics.svelte';
	import FeatureCategoriesView from '$lib/components/settings/subscription/FeatureCategoriesView.svelte';

	export let data: PageData;
	$: ({ subscriptionData, systemSettings, userRole, error } = data);
	$: dominantColor = systemSettings?.DOMINANT_COLOR || '#2563eb';

	// Tab management
	let activeTab: SubscriptionTab = 'overview';
	const availableTabs: SubscriptionTab[] = ['overview', 'plans', 'features', 'usage'];
	const defaultTab: SubscriptionTab = 'overview';
	const TAB_STORAGE_KEY = 'subscription_settings_active_tab';

	// Toast state
	let toastMessage = 'Successfully updated';
	let toastStatus = false;

	// Tab persistence functions
	function saveActiveTab(tab: SubscriptionTab) {
		if (typeof window !== 'undefined') {
			try {
				window.sessionStorage.setItem(TAB_STORAGE_KEY, tab);
			} catch (e) {
				console.warn('Failed to save active tab to sessionStorage:', e);
			}
		}
	}

	function loadActiveTab(): SubscriptionTab {
		if (typeof window !== 'undefined') {
			try {
				const savedTab = window.sessionStorage.getItem(TAB_STORAGE_KEY) as SubscriptionTab;
				return savedTab && availableTabs.includes(savedTab) ? savedTab : defaultTab;
			} catch (e) {
				console.warn('Failed to load active tab from sessionStorage:', e);
				return defaultTab;
			}
		}
		return defaultTab;
	}

	function handleTabChange(newTab: SubscriptionTab) {
		if (activeTab !== newTab && availableTabs.includes(newTab)) {
			activeTab = newTab;
			saveActiveTab(newTab);
		}
	}

	// Initialize active tab on mount
	onMount(() => {
		activeTab = loadActiveTab();

		// Inject dynamic CSS for tab styling
		const styleId = 'subscription-tab-styles';
		let styleEl = document.getElementById(styleId) as HTMLStyleElement;
		if (!styleEl) {
			styleEl = document.createElement('style');
			styleEl.id = styleId;
			document.head.appendChild(styleEl);
		}
		styleEl.textContent = `
			.subscription-tabs [role="tab"][aria-selected="true"] {
				border-bottom: 2px solid ${dominantColor} !important;
				color: ${dominantColor} !important;
			}
			.subscription-tabs [role="tab"]:hover {
				color: ${dominantColor} !important;
			}
		`;
	});

	// Handle subscription actions
	function handleSubscriptionAction(event: CustomEvent) {
		const { action, data: actionData } = event.detail;
		console.log('Subscription action:', action, actionData);
		
		// Show success toast
		toastMessage = `${action} completed successfully`;
		toastStatus = true;
	}
</script>

<svelte:head>
	<title>{t('subscription_management') || 'Subscription Management'}</title>
</svelte:head>

{#if toastStatus}
	<Toast
		id="subscription-toast"
		color="green"
		transition={fly}
		params={{ x: 200 }}
		bind:toastStatus
		class="fixed left-3/4 top-1/4 -translate-x-1/2 -translate-y-1/2 transform"
	>
		<CheckCircleSolid slot="icon" class="h-5 w-5" />
		{toastMessage}
	</Toast>
{/if}

<div class="min-h-screen rounded-lg bg-white">
	<div id="subscription-container" class="mx-auto max-w-7xl px-4 py-10 sm:px-6 lg:px-8">
		<!-- Header -->
		<div class="mb-8">
			<h1 class="text-3xl font-bold text-gray-900">
				{t('subscription_management') || 'Subscription Management'}
			</h1>
			<p class="mt-2 text-gray-600">
				{t('subscription_description') || 'Manage your subscription plan, features, and usage'}
			</p>
		</div>

		{#if error}
			<div class="mb-6 rounded-lg bg-red-50 p-4 border border-red-200">
				<p class="text-red-800">{error}</p>
			</div>
		{:else if subscriptionData}
			<!-- Tab Navigation -->
			<div id="subscription-tab-container" class="bg-white rounded-lg border-b mb-6">
				<div id="subscription-tab-navigation" class="subscription-tabs flex border-b">
					<button
						id="subscription-overview-tab"
						class="px-6 py-4 text-sm font-medium border-b-2 {activeTab !== 'overview' ? 'text-gray-500 hover:text-gray-700' : ''}"
						style={activeTab === 'overview' ? `color: ${dominantColor}; border-color: ${dominantColor};` : ''}
						on:click={() => handleTabChange('overview')}
						aria-selected={activeTab === 'overview'}
						role="tab"
					>
						{t('overview') || 'Overview'}
					</button>

					<button
						id="subscription-plans-tab"
						class="px-6 py-4 text-sm font-medium border-b-2 {activeTab !== 'plans' ? 'text-gray-500 hover:text-gray-700' : ''}"
						style={activeTab === 'plans' ? `color: ${dominantColor}; border-color: ${dominantColor};` : ''}
						on:click={() => handleTabChange('plans')}
						aria-selected={activeTab === 'plans'}
						role="tab"
					>
						{t('plans') || 'Plans'}
					</button>

					<button
						id="subscription-features-tab"
						class="px-6 py-4 text-sm font-medium border-b-2 {activeTab !== 'features' ? 'text-gray-500 hover:text-gray-700' : ''}"
						style={activeTab === 'features' ? `color: ${dominantColor}; border-color: ${dominantColor};` : ''}
						on:click={() => handleTabChange('features')}
						aria-selected={activeTab === 'features'}
						role="tab"
					>
						{t('features') || 'Features'}
					</button>

					<button
						id="subscription-usage-tab"
						class="px-6 py-4 text-sm font-medium border-b-2 {activeTab !== 'usage' ? 'text-gray-500 hover:text-gray-700' : ''}"
						style={activeTab === 'usage' ? `color: ${dominantColor}; border-color: ${dominantColor};` : ''}
						on:click={() => handleTabChange('usage')}
						aria-selected={activeTab === 'usage'}
						role="tab"
					>
						{t('usage') || 'Usage'}
					</button>
				</div>
			</div>

			<!-- Tab Content -->
			<div id="subscription-tab-content">
				{#if activeTab === 'overview'}
					<div id="subscription-overview-content" class="space-y-6">
						<CurrentSubscriptionCard 
							subscription={subscriptionData.currentSubscription}
							notifications={subscriptionData.mockData.notifications}
							on:action={handleSubscriptionAction}
						/>
						<UsageStatistics 
							usageStats={subscriptionData.mockData.usageStats}
							currentTier={subscriptionData.tiers.find(t => t.name === subscriptionData.currentSubscription.tier)}
						/>
					</div>
				{/if}

				{#if activeTab === 'plans'}
					<div id="subscription-plans-content">
						<SubscriptionTiersComparison 
							tiers={subscriptionData.tiers}
							currentTier={subscriptionData.currentSubscription.tier}
							on:action={handleSubscriptionAction}
						/>
					</div>
				{/if}

				{#if activeTab === 'features'}
					<div id="subscription-features-content">
						<FeatureCategoriesView
							categories={subscriptionData.featureCategories}
							tiers={subscriptionData.tiers}
							currentTier={subscriptionData.currentSubscription.tier}
						/>
					</div>
				{/if}

				{#if activeTab === 'usage'}
					<div id="subscription-usage-content">
						<UsageStatistics
							usageStats={subscriptionData.mockData.usageStats}
							currentTier={subscriptionData.tiers.find(t => t.name === subscriptionData.currentSubscription.tier)}
							detailed={true}
						/>
					</div>
				{/if}
			</div>
		{:else}
			<div class="text-center py-12">
				<p class="text-gray-500">Loading subscription data...</p>
			</div>
		{/if}
	</div>
</div>
