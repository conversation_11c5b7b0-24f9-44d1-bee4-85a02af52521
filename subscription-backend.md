# Subscription System

A subscription-based user quota system that integrates with the existing SystemSettings model to manage organization subscriptions, user quotas, and feature access.

## Overview

The subscription system provides:
- **User Quota Management**: Limits the number of active users based on subscription tier
- **Feature Access Control**: Controls access to premium features based on subscription
- **Subscription Status Tracking**: Monitors subscription status and expiry
- **API Integration**: RESTful APIs for quota validation and subscription management

## Architecture

### Data Storage
- Uses existing `SystemSettings` model with key `SUBSCRIPTION_DATA`
- Stores subscription data as JSON in the `value` field
- No additional database tables required

### Components
1. **SubscriptionDataService**: Manages subscription data storage and retrieval
2. **QuotaValidationService**: <PERSON>les quota validation and feature access checks
3. **API Views**: RESTful endpoints for subscription management
4. **User Creation Integration**: Validates quotas during user creation

## JSON Data Structure

```json
{
    "organization_name": "Viriyah",
    "subscription_key": "ENTP-C3D4E5F6G7H8I9J0",
    "tier_id": "enterprise_plus",
    "tier_name": "Enterprise Plus",
    "status": "active",
    "activated_on": "2025-08-29T22:15:00Z",
    "expires_at": "2025-12-31T23:59:59Z",
    "quota": {
        "max_active_users": 50,
        "max_line_accounts": "unlimited",
        "max_ai_workflow_units": "unlimited",
        "max_messages_per_min": 200,
        "max_storage_gb": 2000
    },
    "features": {
        "custom_transfer_algo": true,
        "custom_case_desc": true,
        "custom_ai_workflow": true,
        "ai_quick_reply": true,
        "ai_smart_reply": true,
        "ai_memory": true,
        "crm_integration": true,
        "crm_notify_claim": true,
        "crm_case_system": true,
        "dashboard_sla_config": true,
        "dashboard_sla_alert": true,
        "broadcasting": true
    },
    "metadata": {
        "billing_contact": "<EMAIL>",
        "technical_contact": "<EMAIL>"
    }
}
```

## API Endpoints

### 1. Get Quota Status
```
GET /api/subscription/quota/status/
```
Returns current quota usage and limits.

**Response:**
```json
{
    "message": "Quota status retrieved successfully",
    "data": {
        "organization_name": "Viriyah",
        "tier_name": "Enterprise Plus",
        "status": "active",
        "expires_at": "2025-12-31T23:59:59Z",
        "quota": {
            "max_active_users": 50,
            "current_active_users": 15,
            "remaining_slots": 35,
            "max_line_accounts": "unlimited",
            "max_ai_workflow_units": "unlimited",
            "max_messages_per_min": 200,
            "max_storage_gb": 2000
        },
        "features": { ... }
    }
}
```

### 2. Check User Creation
```
GET /api/subscription/quota/check/
```
Validates if a new user can be created.

**Response:**
```json
{
    "message": "User creation allowed",
    "data": {
        "allowed": true,
        "quota_info": {
            "current_users": 15,
            "max_users": 50,
            "remaining_slots": 35,
            "tier_name": "Enterprise Plus",
            "organization_name": "Viriyah"
        }
    }
}
```

### 3. Check Feature Access
```
GET /api/subscription/feature/check/?feature=ai_smart_reply
```
Checks if a specific feature is enabled.

**Response:**
```json
{
    "message": "Feature access checked successfully",
    "data": {
        "feature": "ai_smart_reply",
        "has_access": true
    }
}
```

### 4. Get Subscription Info
```
GET /api/subscription/info/
```
Returns subscription information (filtered based on user permissions).

## Usage Examples

### Initialize Subscription Data
```bash
python manage.py init_subscription --organization-name "Your Company" --tier enterprise_plus --max-users 50
```

### Check Quota in Code
```python
from subscription.services import QuotaValidationService

# Check if user creation is allowed
result = QuotaValidationService.can_create_user()
if result['allowed']:
    # Create user
    pass
else:
    # Handle quota exceeded
    print(result['error'])
```

### Check Feature Access
```python
from subscription.services import QuotaValidationService

# Check if feature is available
has_access = QuotaValidationService.check_feature_access('ai_smart_reply')
if has_access:
    # Enable feature
    pass
```

## Integration with User Creation

The system automatically integrates with the existing `SignUpView` in `user/views.py`:

1. **Pre-validation**: Checks quota before user creation
2. **Error Handling**: Returns appropriate error messages when quota is exceeded
3. **Response Enhancement**: Includes quota information in successful responses

## Error Handling

The system handles various error scenarios:

- **No Subscription**: Returns 404 when no subscription is found
- **Expired Subscription**: Blocks operations when subscription has expired
- **Quota Exceeded**: Returns 403 when user limit is reached
- **Invalid Features**: Returns false for non-existent or disabled features

## Testing

Run the test suite:
```bash
python manage.py test subscription
```

The test suite covers:
- Service class functionality
- API endpoint responses
- Quota validation logic
- Feature access control
- Error scenarios

## Security

- **Permission-based Access**: API endpoints require authentication
- **Data Filtering**: Non-admin users see limited subscription information
- **Input Validation**: All inputs are validated before processing
- **Error Logging**: Comprehensive logging for debugging and monitoring
